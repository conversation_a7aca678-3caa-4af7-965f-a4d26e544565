[July 25 2025]
  - <PERSON><PERSON><PERSON>: race bug in character creation for luminari
  - <PERSON><PERSON><PERSON>: 2 undefined races added
  - <PERSON><PERSON><PERSON>: Update objsave.c
  - <PERSON><PERSON><PERSON>: another tokenize fix attempt
  - <PERSON><PERSON><PERSON>: Update TOKENIZE_BUG_NOTES.txt
  - Zus<PERSON>: another attempt to fix tokenize
  - <PERSON><PERSON><PERSON>: Update objsave.c
  - <PERSON><PERSON><PERSON>: added debugging for tokenizer
  - <PERSON><PERSON><PERSON>: pre c99 fix oops
  - <PERSON>usuk: Fix memory allocation failures in tokenize() causing server crashes
  - <PERSON><PERSON><PERSON>: Update TASK_LIST.md
  - Zusuk: Update CHANGELOG.md
  - Zusuk: Fix memory corruption in tokenize() causing crash on house load
  - <PERSON>usuk: Fix compiler warnings
  - <PERSON><PERSON><PERSON>:   Fix critical use-after-free bug in close_socket() and major memory leak in tokenize()
  - <PERSON>usuk: Fix memory leaks in tokenize() function usage across multiple files
  - Zusuk:   Fix memory leaks in quest system and tokenize function
  - <PERSON>usuk: documentatoin update
  - Zusuk: Fix compiler warnings
  - <PERSON><PERSON>uk: Fix compiler warnings (86→6)
  - <PERSON>usuk: Fix critical use-after-free bug in list iterator system
  - <PERSON>usuk: Fix critical 460KB memory leak in object save system
  - <PERSON><PERSON><PERSON>: Fix NPC -> from player_specials access violations
[July 24 2025]
  - <PERSON><PERSON><PERSON>: updated perfmon
  - Zusuk: updated task list
  - Zusuk:   Fixed uninitialized rnum field in board_info array causing find_board() failures.
  - Zusuk: Update db.c
  - Zusuk: perf: Optimize do_save() and affect_update() for performance improvement
  - Zusuk: Security: Complete PHP tools audit and modernization | perf: Optimize affect_update() to skip unaffected NPCs
  - Zusuk: perf: Optimize NPC out-of-combat buffing to reduce CPU overhead
  - Zusuk: perf: Optimize mobile_activity() with safe caching improvements
  - Zusuk: fix: Optimize zone reset chest placement performance from O(n²) to O(n)
  - Zusuk: converted documentation to markdown
  - Zusuk: update to README & disabled test file
  - Zusuk:   fix: Prevent NPCs from accessing player-only data structures + documentation conersions
  - Zusuk: updated perfmon
  - Zusuk: needed to add vnums.h include to crafting_new + documentation comments
  - Zusuk: reduce buffer sizes in objsave
  - Zusuk:   fix: Remove invalid spec proc assignments to non-existent mobs/objects
  - Zusuk:   fix: Implement award_magic_item() and ensure crafting system visibility
  - Zusuk:   fix: Resolve combat corpse targeting errors
  - Zusuk: fix: Prevent NPCs from accessing player preference flags
  - Zusuk: create worklist - working through syslog errors
  - Zusuk:   fix: Resolve syslog errors and add missing configuration options
  - Zusuk: Update objsave.c
  - Zusuk:   fix attempt: Resolve error spam in combat, scripts, and database operations
  - Zusuk: cleanup of docs
  - Zusuk:   fix: Add NPC safety checks to PRF_FLAGGED usage to prevent crashes
  - Zusuk: Update constants.h
  - Zusuk: expanded constants.c arrays + attempt to fix feats info dragon mount
[July 23 2025]
  - Zusuk:   fix: Move variable declaration to proper scope in class.c
  - moshehbenavraham: Make cpbin2dev.sh executable
  - Zusuk: Update settings.local.json
  - Zusuk:   fix: Add comprehensive DG Scripts bounds checking and fix VALID_ROOM_RNUM
  - Zusuk:   fix: Add database compatibility and script crash protection
  - Zusuk: just adding success message to mysql connection
  - Zusuk: more tweaks trying to get dev server to work
  - Zusuk: dev script tweak
  - Zusuk: handle missing house DB for dev server
  - Zusuk: shortened script name
  - Zusuk: two warnings fixed
  - Zusuk: fixing easy to fix safe warnings
  - Zusuk: minor tweaks to get code to compile
  - Zusuk: Fix attempt multi-word search bug in feat/evolution/skill/ability/discovery commands
[July 21 2025]
  - Zusuk: just added comment to point dev in right direction for PC hp calculation
  - Zusuk: vampire were not getting legendary race hp bonus
  - Zusuk: song of dragons is broken at least on luminari
[July 14 2025]
  - GickerLDS: Fixed some merge conflicts and errors.
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Added Initiative to score. Fixed a bug where object edescs were being cut off. Added sheath wear slot. Added sheath and draw commands. Implemented new newbie gear. Enforced new object bonus guidelines on crafted items. Added wearapplies and wearlocations commands. Added mud_options and vnums example files. Added random treasure drops for ankle, face, shoulder, eyes and ears. Random treasure drops now follow new object guidelines.
  - Zusuk: formatting fixes
[July 10 2025]
  - Zusuk: added clang-format
  - Zusuk: adding claude-code settings
[July 06 2025]
  - GickerLDS: The number of bonus types (apply_) per wear slot is now tracked and shown in oedit. Added two php files that can be added to web site to show bonus breakdowns. saveobjstodb command now doable by level 33+ staff Bonus amounts and type guidelines now displayed in oedit. Added ability to add random bonuses to items that obey guidelines in building documents (Chronicles of Krynn)
[June 26 2025]
  - GickerLDS: Treasure chest items now offer instructions when looked at. Get and put commands can now be used with the bag system. Improved checks for swimming. Added checks for climing, inluding damage for falling, with ways to avoid that damage. Improved walkto system, which checks for certain situations in which you could not automatically proceed with walkto alone Adjusted affect and text for slow fall Added function to count rooms between two points Added number of rooms distance to locations in landmarks command. Added guidelines for setting object bonuses in oedit, shown to the builders while editing objects. So they don't need to refer to a pdf documentation. This is based on Meka's ability documents in the staff building discord.
[June 24 2025]
  - GickerLDS: Added ankle wear slot (right and left) Added code for swarm type mobs More crafting system work.
[June 09 2025]
  - GickerLDS: More crafting bug fixes. Two handed weapons double affect modifiers Landmark and walkto systems added for Krynn.
[June 04 2025]
  - GickerLDS: More crafting fixes. Added crafting level adjustment.
[June 03 2025]
  - GickerLDS: Added a define for default instrument breakability value.
[June 02 2025]
  - GickerLDS: Some bug fixing for crafting instruments.
  - GickerLDS: New crafting system (90% done)
[April 14 2025]
  - GickerLDS: Basics of Crafting System Implemented and Debugged.
[February 11 2025]
  - GickerLDS: -Spellswords can now remove spells on their weapons with the channelspell command. -Added great intelligence as an epic class feat for psionicists. -Work on the new crafting system. Recipes added. Harvest nodes being assigned, added. -Added a room flag that will make sure a harvest node loads there every time. -Removed drow and orc encounters for dragolance setting. -Builders cannot change the builder list on their zones unless they're level 33+ -Added a documentation folder and existing documentation for the codebase.
[December 30 2024]
  - GickerLDS: -Added beginnings of introduction system -Added Intimidate as a Ranger class skill -Channel spell is now obtained by spell swords at a lower level -Greater channeling now increases chance of proc to 10% -In DL version, spellcaster mobs no longer buff outside of combat unless flagged to do so. -Added paralysis immunity flag for mobs. -Added bozak draconian advanced race. -Changed affect of poisoned status to reduce attack roll and armor class as well as hp and mv regen rate. -Races now sorted alphabetically in character creation. -Added checks for paralysis immunity to a number of effects. -Added check to remove duplicate quests in a player's quest list.
[November 29 2024]
  - GickerLDS: -Added gully dwarf race -Added race and level back to who list -Added the following bonus types: spell circles (1-9), spell potency, spell dc, spell duration, spell penetration -Added lastroom command -Added listraces command
[November 13 2024]
  - GickerLDS: -Added the lastroom command, which allows players who enter the game to the default start room, to recall back to the last room they were in prior to the system restart that caused them to enter via the default start room. -Added the following new bonus locations: Extra Spell Slots (1-9), Improved Spell Potency (Damage and Healing), Improved Spell Duration, Improved Spell DC. -Added extra spell information to the score, for the new spell bonus locations. -Removed the (OLC) flag when players are in the study menu. -Added the following epic feats: Bane of Enemies, Death of Enemies, Bulwark of Defense, Chaotic Rage, Colossal Wild Shape, Epic Damage Reduction, Deafening Song, Devestating Critical, Gargantuan Wild Shape, and Overwhelming Critical. -Added ability for staff to set a player's background archtype with the set command. -Added the epicfeats command, displaying all epic feats and their prerequisites, to assist with epic level build planning. -Added some of the epic feats as epic class feats for certain classees. See for more info: https://www.d20srd.org/srd/epic/classProgressions.htm -Bonus exp for leadership feat ranks will now display properly (it was being applied, but not added to the displayed total). -Disabled ability score damage causing you to go unconscious. -Random treasure can now apply any bonus type to any wear slot. -Added the following bonus types to random treasure: spell dc, spell slots (1-9), spell potency, spell duration, hp regen, mv regen, psp regen, encumbrance, fast healing, initiative, skills bonuses.
[October 22 2024]
  - GickerLDS: -Added the roomvnum command, allowing players to get the room vnum for the room they're in. -Changed autoblast command to assistblast, and added the autoblast toggle so that warlocks don't have to use the blast command to commence using their eldritch blast in place of regular attacks. -Added more prominent display for characters who are flagged non-roleplayers, when seen in room. This is to alert role players in that room that this person is not a role player. -Added background archtypes. Each archtype gives a bonus to two skills, and a new ability. The archtypes are: Acolyte, Charlatan, Criminal/Spy, Entertainer, Folk Hero, Gladiator, Hermit, Noble, Outlander, Pirate, Sage, Sailor, Soldier, Squire, Trader and Urchin. -Characters can now choose their character's age from the following list: adolescent, adult, middle-aged, old-aged, venerable. Age is no longer tracked mechanically as a number, but rather as the listed descriptions instead, and does not change except through role-playing. Each age has certain ability score modifiers, except adult, which is +0 at all ability scores. -Added the postcombatbrief toggle, which will reduce the amount of post-combat text to cut down on text-spam. -Turn undead, when used on players, will now deal damage instead of causing them to flee or being insta-killed with large level differentials between player and mob. -Fixed a bug with paladin mercies that would sometimes crash the game. -Blackguards now get a series of class abilities that improve the save DCs on their cruetly uses. They also now get a mount they can summon with the 'call mount' command. -Made adjustments to the skill list. Some skills were combined, some new ones added, and some were renamed. -Adjusted the lore command to perform a d20 roll and also use different lore skills depending on what is being identified (Arcana, Religion or Nature). -Removed Ascii art on copyover. -Scythes are now slashing weapons only. Previously they were slashing and piercing. -Added character age to a character's short description. -Swapped out Great Int for Great Charisma for Sorcerer epic class feats. -Added a new game menu for setting optional character details. Among these are: short description, detailed description, background story, background archtype, character goals, character personality, character ideals, character bonds, character flaws, character age, homeland, hometown, faction, and deity. -New characters will now be prompted to flag themselves as non role players or role players, or defer the decision to later (which can be done in the prefedit menu). -Reduced exp gained by summoners for damage dealt by their eidolons in combat. -Fixed Anti-Neutral object flag to no longer exclude NG and NE alignments. -Added background descriptions to the help system. -kill command is now just an alias for the hit command. -Added the 'rpsheet' command which will display information pertinent to role playing for that character. Staff members can view the rpsheets of other players. -Fixed a bug with the holy aura command that caused crashes sometimes. -Fixed a bug with 'see the unseen' warlock spell that prevented it from working correctly. -Players casting healing spells on undead group members will no longer damage them. -Fixed some bugs with extra descs on some items causing crashes when their owners enter the game. -Fixed cooldown on the summoner 'merge forms' ability. -Coded in the mob ability 'corruption' -Smite Evil/Good/Destruction will all offer bonuses regardless of alignment, with greater bonuses if used against the listed alignment.
[September 24 2024]
  - GickerLDS: Added relay ability to send messages to people in other locations. Added forgeas to allow for attempting to forge messages to others. Lore checks are now rolls against a set dc. Object lore checks now use the highest of your arcana, religion and nature skills. Mob lore checks now use either arcnana, religion or nature skill depending on the race type of the mob. Added ability for sages to give a bonus to attacks and damage for party members against mobs they've lore'd. Fixed a bug with autostore. Redid the skill lists to be alphabetical. Added backgrounds for: Acolyte, Charlatan, Criminal, Entertainer, Folk Hero, Gladiator, Hermit, Noble, Outlander, Pirate, Sage and Trader. Added the ability to get coin and possibly treasure through the entertain, tribute, swindle and extort commands for various backgrounds. Added the ability to forage for food and drink in the wild. Renamed Survival skill to Nature. Added ability to add multiple clan allies or at-war enemies. Fixed class and cross-class skills for each class based on the new skills list. Added ability for builders to set city, nation and faction for zones. Gear given to and equipped by charmie will now save over reboots. Added black market and noble-only shop flags/types.
[September 11 2024]
  - GickerLDS: Added downgrade command to downgrade high level items to lower level usage, including reduction of stats in the process. Fixed auto prep to cycle through different spellcasting classes when you rest, auto-prepping them all. Identifying an item now flags it identified and can henchforth be identified by anyone regardless of Arcana skill. Added a saveeverything command on the rare occasion we need to save all zones. Began the background system a la 'D&D 5e' system. Changed various skill names, combined some skills, and added some new skills. Monk weapons now do monk barehand damage if barehand damage dice are higher than the weapon dice. Added a temple spec proc that offers healing for gold. All warlock blast essences have lower durations, cooldowns on how often they can be applied to a single target, and can be stacked with each other. Fixed duration on see the unseen warlock spell. Eldritch shape and essence now save over sessions.
[September 01 2024]
  - GickerLDS: Added item activated spells. Addes redesc command. Can now restring items with edescs. Can now buy knuckles from bazaar
[August 31 2024]
  - GickerLDS: Added toggle to contain AoE effects to only those you are already fighting. Added mass identify spell Added flag to prevent items from being sacrificed and destroyed. Added sortto all and sortfrom all Mounted combat feat now prevents being thrown from your horse while moving around. Added footman's lance weapon type. Added empower, extend spell, still spell and silent spell feats. Added automatic silent, automatic quicken and automatic still spell epic feats. Infravision now functions same as ultravision. Added ability to create items that can be used to cast certain spells a certain number of times. Abilities command now shows spells that can be activated from an equipped object.
[August 21 2024]
  - GickerLDS: Bug fixes. Buildwalk extended functionality. Attacks bonus breakdown Added more languages for Dragonlance Added extract after use item flag for one-time-use keys Increased duration of charm spells Added psionicist NPC AI
[August 07 2024]
  - GickerLDS: Fixed some bugs and typos. [Aug 07 2024] - Gicker   Added functionality allowing summoner eidolons to cast the spells provided in the evolutions: basic magic, minor magic, major magic and web. Simply order eidolon cast 'spellname' <target> to use [Aug 06 2024] - Gicker   Melee damage done by eidolon will grant exp to the summoner as if it were the summoner hitting the enemy   Enlarge person now properly applies +2 str and -2 dex   Eidolon attack damage now displays correctly in attacks command and scales with eidolon size [Aug 02 2024] - Gicker   Added flame arrow spell   Made fireball an AoE spell with damage reflective of source material.   Fixed a bug that was causing a crash sometimes when typing score, related to encumberance bonus changes.   Modified the following spells to make them behave more similarly to the source material: scorching ray, lesser missile storm, missile storm
[July 30 2024]
  - GickerLDS:   Fixed issue with encumbrance bonuses stacking infinitely   Stone skin and other damage absorbing spells will now absorb spell damage.   Fixed an issue where psionic DC bonus augments would stack with successive psionic ability manifestations   Fixed duration on human potential spell   Fixed the missing DC bonus on the psionic ability: psychosis   Fixed a number of charm spells to prevent the chance of the charmee entering battle against the charmer on a successful casting
  - GickerLDS: [Jul 30 2024] - Gicker   Fixed a bug with the spells command not working right in some situations.   Some healing spells cast on undead will now harm them. Conversely, some cause X wounds spells cast on undead will heal them.   Fixed weapon specialization feat prerequisites   Hints have been updated to coincide with Chronicles of Krynn specifics.   Fixed a bug allowing eidolons to see in dark in order to allow them to follow orders given by their masters. [Jul 29 2024] - Gicker   Fixed shop sell rates to prevent money gain loopholes   grapple should now show in maneuvers list for both pcs and npcs   Shadow body psionic power should now properly bestow immune to critical hits.   Fixed an error where max augment psp was being displayed incorrectly in score.   Fixed master of the mind feat to work with most psionic powers now.   Wildshaping into a fire elemental now provides light in the room you occupy.
[July 08 2024]
  - GickerLDS: Added quest dialogue system Added darken wood as dragonlance carriage locale mob skills are now 75% of their level
[May 28 2024]
  - GickerLDS: Changelog May 28, 2024
[May 02 2024]
  - GickerLDS: Added knight of the lily class.
[April 30 2024]
  - GickerLDS: Added Knight of the Skull class.
  - GickerLDS:   Added the Knight of the Thorn class   areas command now shows zone number   Added the ability for staff to set a custom 'clan title' in place of where mortals show their clan in the who list.   Added 7 starting clans. If you wish to start a new clan, please speak to Gicker.   Changed the who list to show only your clan (or 'Adventurer' if unclanned) and your title   Fixed the title system, so you can put your name anywhere in the title.   Fixed some issues trying to target players sometimes
[April 26 2024]
  - GickerLDS:   You can now 'store all' to store all consumable items in your inventory to their storage list.   You can now 'sortto all' to sort all items in your inventory to the associated bag.   Added the autostore option. See HELP AUTOSTORE.   Added the autosort option. See HELP AUTOSORT.   Added a number of new areas to the areas command.   When using the areas command, areas will now list in alphabetical order.
[April 24 2024]
  - GickerLDS: You should now be able to see and manipulate your inventory even when you can't see (darkness, blinded). Added the kapak draconian advanced race Added ability for charmies to perform more abilities and commands that were previously not performable my NPCs, grapple being one of them. Race restrictions removed from arcane archer Created a new object database for builder info and for players to find gear: https://krynn.d20mud.com/objectdb/ Functions that enable searching for players and mobs will prioritize players over mobs now
[April 10 2024]
  - GickerLDS: [Apr 10 2024] - Gicker   Added the leadership feat.   Speed can now affect combat bonuses. See HELP SPEED for more info.   Characters can now multiclass with up to 5 different classes, up from 3.   Added Knight of the Rose class [Apr 01 2024] - Gicker   Added the holy aura spell for clerics   Fixed a bug where Sometimes character advancement bugged out by an unspent class feat.   Increase max number of classes per character for multiclassing from 3 to 4   Added the Knight of the Sword prestige class [Mar 30 2024] - Gicker   Modified the 'class' command (class info, class feats, etc.) so that you can now use spaces when specifying the class name to view info on.   Added the Knight of the Crown prestige class. Has no account experience cost. See more info with 'class info knight of the crown'   Barghest and Banshee hunt trophies were reversed by mistake. This is now fixed.   Temporarily disabled the etheral shift travel domain power as we don't have planar travel and need to recode this ability.
[March 22 2024]
  - GickerLDS: Added Goblin and Hobgoblin races to dragonlance campaign setting. Added code to replace lost quest items. Fixed a bug where grouped charmies were still not counting their kills toward quest goals
[February 28 2024]
  - GickerLDS: -Added random encounter system to Dragonlance campaign setting -Added mission system to Dragonlance campaign setting -Added hunts system to Dragonlance campaign setting -Necromancers can now summon their undead cohort even without levels in summoner. -Added a series of feats that improve warlock power damage and DCs. -Added level 30 warlock feat 'eldritch master' -Can now craft with dragon hide/scales/bones in Dragonlance Campaign. -Changed defending weapon AC bonus for enhancement type to universal type. -Added min_dice function for coders, so they can roll dice which rerolls any dice that come up less than a specified number.
[February 07 2024]
  - GickerLDS: Increased mission exp award amount for DL campaign.
  - GickerLDS: Added the mission system for the Dragonlance Campaign.
[February 05 2024]
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: -Reinstated level limits on gear. -Fixed a bug where an objects min level to use has to be between 1 and 30. -Added ability to use the charge command without a target if autohit is enabled. -Fixed eidolon custom descs. -Added ability for psionic powers to be buffed again. -Added autoaugment toggle so that psionic powers manifested via buff command will use max augment psp allowed. -Added rapid buff effect and potions to Palanthas and Sanction. These will greatly reduce the time taken to buff yourself with the buff command. -Gave spellsword class 2 more class feats and add 1/2 their levels to fighter levels in determining feat prerequisites. -Spellswords now get 3 channelling spells (regular/improved/greater) which will increase the saving throw DCs of any spell triggered through their weapon proccing ability. -Reduced experience required to level significantly for the Dragonlance campaign. -Added power word silence spell. -Fixed a bug with reforge which was checking for gold requirement too late in the process. -Healing touch ability no longer requires target to be 1/2 health, Dragonlance campaign only. -Power word blind, stun and kill are new war domain spells for 7,8, and 9th circles. -Fixed a bug with touch of undeath ability. -Added a damage cap for NPCs on Dragonlance campaign only. -Increased duration on assimilate psionic power. -Last player online lists updated now as soon as any player logs into the mud, inseatd of on a pulse timer. -Increased rate of PSP recovery for psionicists. -Psionicists now add their Intelligence bonus to psp regen amounts. -Added ability to clear all special weapon abilities, as well as properly save when editing them. -Added ability to clear all object extra effects/flags with single command. -Fiendish boon now effects all targets, instead of only good targets. -Time stop allows players to cast spells without using actions. -Geniekind spell effects can no longer stack. -Increase gold on random treasure drops. Dragonlance campaign only.
[January 22 2024]
  - Steve Squires: Merge pull request #36 from melvinzhang/melvinzhang-patch-1
[January 21 2024]
  - Melvin Zhang: Update feats.c
[December 21 2023]
  - Steve Squires: Merge pull request #35 from melvinzhang/melvinzhang-patch-1
  - GickerLDS: Removed extra line break
  - Melvin Zhang: fix name of evolution
[December 20 2023]
  - GickerLDS: Fixed some goto crashes.
  - GickerLDS: Some code fixes for FR base.
  - GickerLDS: Added the reforge command.
[December 19 2023]
  - GickerLDS: Fixed some luminari-specific bugs,
  - GickerLDS: Added a cooldown on call eidolon. Can now use look direction and dig direction using shorthand for diagonals, ie. ne nw se sw Added NOMAP zone flag to prevent people from being able to use automap, map or scan in such areas. Added Necromancer class and associated abilities. Most eidolon related mechanics now combine summoner and necromancer level for effective level. Fixed a bug with goto staff command sometimes crashing the mud. Added support for moving rooms. OLC aspect not yet coded. Fixed a bug with track command that sometimes crashed us. Staff can now clear room flags on a room by typing -1 in the room flags screen of redit. Psionic powers boday of iron and shadow body now properly negate critical hits when active. Fixed a supplyorders issue that was affecting non-dragonlance versions of the code improperly.
[December 08 2023]
  - GickerLDS: - Using lore on a mob now shows hp and max hp. - Legacy style drink containers and fountains can now be used to drink from, gaining a buff based on the kind of liquid in the container/fountain. - Pick lock now requires at least one point spent in disable device skill. - Added unstuck command that will recall a character to the game's mortal start room (set in cedit command) at a cost of exp and gold for characters over level 10. Below 10, it is free. - Fixed a number of typos in the code. - Put a cooldown on being 'slammed' to prevent balance issues of spamming it. - Adjusted how max number of charmies is calculated. - Made a fix so that mobs can benefit from affects that increase their Max HP. - Spells that increase maximum hit points now increase hp by the same amount. When they wear off, they lose the extra hp as well, but will not drop them below 1 hp. - Entangle spell is now blocked by freedom of movement. - Reduced the duration of paralysis and stun affects across the board. - Fixed how channel energy levels are calculated. - Summon creature/nature's ally 7, 8 and 9 now have different stats, with the higher level versions being more powerful. - MUDlet package no longer auto downloads on DL version. - Magic staff consumables now working as intended. - Increased the enchantment level of random magic items for Dragonlance codebase only. - Metamagic adept should now work properly and show up properly on abilities command.
[November 05 2023]
  - GickerLDS: Fixed a few more bugs: outfits, accexp advanced race unlocks, added autocon to default new character flags, reduced exp requirements for advanced races in dragonlance base.
[November 02 2023]
  - GickerLDS: Fixed some compiler errors
  - GickerLDS: Fixed some run time errors using the default campaign
  - GickerLDS: Fixed some compiler errors for missing defines in luminari base
  - GickerLDS: Fixed a bug with assassin's mark command. Made Mummy Dust a divine epic spell. Fixed some issues with psp and stamina regen. Fixed some item vnums for mag_creations and mag_summons spells for DL campaign only. Fixed a bug with premade warlocks that was giving them a psionic only feat at level one by mistake. Fixed the mold object vnum for DL campaign only.
[October 30 2023]
  - GickerLDS: Fixed bugs with fast healing and hp regen modifiers. If you are riding a mount who is flying or levitating, you benefit from the same for movement purposes. Fixed blocker flags all defaulting to block east. Added block evil, neutral and good flags. Added unstuck command. Fixed ital strike. Fixed a problem with spells command when recently gained a level in a non-spellcasting class. Improved supplyorder rewards and limited them to 10 per real 24 day. Resets at server midnight. Reduced mobile spell damage. Fixed a bug with virtual bag item duplicating. Made the @ symbol on its own line an additional way to save text in the text editor, in additon to /s Added dragonlance regions. Reduced duration on ability score paralysis (when an ability scorfe drop below 1) Reduced duration on all paralysis and stun spells and effects to be more manageable in combat. Many were essentially a death sentence because they lasted so long. Fixed a number of creature summoning type spells. Fixed issue with concealing amorpha not being able to target another creature when using sufficient augment psp. Fixed mountain dwarf (dragonlance) racial strength modifier from +1 to +2. Adjusted fast travel durations. Fixed channel energy for inquisitors and blackguards.
[October 19 2023]
  - GickerLDS: [Oct 19 2023] - Gicker   Added a sailing point for dragonlance to northern ergoth   Container objects (ie. backpacks) are currently disabled until the bug where their contents getting deleted sometimes, is fixed. Until then, please use our virtual bag system. See HELP BAGS. Max No. of carried items has been increased to 1,000 in the interim.   Fireside Tavern zone fully converted. Lvls 18-25. Located off the new sea road between Solace and the New Sea Docks in Abanasinia. Carriage Locale in Abanasinia region as well.   Cultists of Morgion zone fully converted. Lvls 18-25. Located just off the path between wenfyr mansion and vingaard keep, south of palanthas. [Oct 18 2023] - Gicker   Fixed a bug with identify mobs causing crashes.   Fixed a bug where random treasure items would stack in a shop list if they're the same type. Eg. 3 random treasure half plate legs would show up as only the first one's description with a quantity of 3. Now they will all show up separately. [Oct 17 2023] - Gicker   Fixed a bug where sometimes inventory virtual bags would duplicate their contents.   fixed the prerequisites for vital strike, improved vital strike and greater vital strike.   fixed a bug that prevented players from levelling sometimes. [Oct 16 2023] - Gicker   coded a fix so that base attack bonus is correctly calculated when determining number of attacks per round. Characters already over level 20 will not be affected by this change unless they respec.   Changed the quest and zone text in the Bethel Island quests to target fishermen instead of shueball players (kids).
[October 15 2023]
  - GickerLDS: Added A LOT of dragonlance theme specific code.
[July 29 2023]
  - GickerLDS: Added a bunch of new weapon poisons.
[June 26 2023]
  - GickerLDS: Finished virtual bags.
[May 09 2023]
  - GickerLDS: Attempted bug fix for saving pets.
[May 02 2023]
  - GickerLDS: Monkey grip is no longer available to goliaths due to their 'powerful build' racial feat. Purified calling spell should now heal proper amounts. Creeping doom spell should now have proper duration for summoners. Updated life bond help file. Altered encumbrance values, will take into account all different size.  Tables more closely match PF 1e rules now.
[May 01 2023]
  - GickerLDS: Eidolons now have their evolutions applied when loaded as a pet between play sessions. Eidolon evolution attacks now use the proper attack type message.
[April 26 2023]
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Added a clause to compute_concealment to check if the attacker (if there is one at the time of computation) can see the person, and if they can't then the concealment is 50%.
  - GickerLDS: Fixed a bug that wasn't allowing alchemists to use the concoct command. Fixed a bug where summoner level was not being calculated properly for their healing spells.
[April 24 2023]
  - GickerLDS: Removed debugging messages.
  - GickerLDS: Fixed an issue with ability score bonuses for eidolons were not applying. Also fixed an issue where pet ability scores were resetting to default after loading the pet after a crash/reboot/copyover/quit session.
[April 20 2023]
  - GickerLDS: Fix for summoner spell prep.
  - GickerLDS: Summoner bug fixes. Critical feats fix attempt.
[April 19 2023]
  - GickerLDS: Exp fix for summoners.
  - GickerLDS: Added starting gear for summoner. Fixed evolution help for thrash evil/good.
  - GickerLDS: Final edits for summoner.  Ready to go into the game for play testing.
[April 16 2023]
  - GickerLDS: More work on Summoner, mostly bug fixes. Still not ready for play.
[April 11 2023]
  - GickerLDS: Finished base code for summoner. Still needs both testing and premade build set up.  NOT YET READY FOR PLAYERS.
[March 28 2023]
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Summoner class evolutions and most feats cmpleted.
[March 16 2023]
  - GickerLDS: Added a fix for autoblast which was causing crashes
[March 15 2023]
  - GickerLDS: Mobs can now use autohit if they are charmees and their master has it turned on.
[March 10 2023]
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Fixed a bug with NULLed lists crashing the mud.
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Added placeholders for summoner abilities and eidolon abilities.
  - GickerLDS: Fixed greater heroism bonus types, added fear immunity to it as well. Fixed a bug with empty target_lists being freed in warlock eldritch blast.
[March 08 2023]
  - GickerLDS: feat info command now shows: feats that this feat is a prerequisite for, races that get the feat, classes that get the feat, classes for which the feat is a class feat.
[March 04 2023]
  - GickerLDS: Fixed a bug with treasure chests causing crash on some zone resets.
  - GickerLDS: Added the grand destiny spell. Double weapon feats are now class feats for warriors and eldritch knights. Added room and zone flags for random treasure chests. Rooms flagged as random chests will load a random treasure chest each zone reset, as long as there isn't already one in the room. Zones flagged random chests will load a chest in a random room within the zone, each time the zone resets. Random chests can also be hidden or locked.  Hidden chests can be uncovered with the search command, and locked chests can be opened with the picklock command.
[March 02 2023]
  - GickerLDS: Some starter code for making randomly loaded chests.
  - GickerLDS: Fixed different critical feats. Fixed planar soul surge effect.
  - GickerLDS: Fixed prereq for stunning critical.
[March 01 2023]
  - GickerLDS: Merge branch 'master' of https://github.com/LuminariMUD/Luminari-Source
  - GickerLDS: Added the following feats: double weapon focus, double weapon specialization, double weapon defense, double weapon critical Fixed the following criticals, which are now selectable: bleeding critical, stunning critical, staggering critical, censoring critical, sickening critical, critical mastery
  - GickerLDS: Fixed bugs with locate creature and paladin mercies.
